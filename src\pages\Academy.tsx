import { useState } from "react";
import { ArrowLeft, Search, Heart, MessageCircle, Share } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";

interface VideoCard {
  id: number;
  title: string;
  duration: string;
  thumbnail: string;
  likes: number;
  comments: number;
  shares: number;
  category: string;
}

const mockVideos: VideoCard[] = [
  {
    id: 1,
    title: "Adet Döngü<PERSON>ü ve Hormonlar",
    duration: "8:30",
    thumbnail: "/api/placeholder/358/201",
    likes: 123,
    comments: 45,
    shares: 67,
    category: "Adet Döngüsü",
  },
  {
    id: 2,
    title: "Doğum Kontrol Yöntemleri",
    duration: "12:45",
    thumbnail: "/api/placeholder/358/201",
    likes: 89,
    comments: 23,
    shares: 45,
    category: "Gebelik",
  },
];

const categories = [
  "Tümü",
  "Adet Döngüsü",
  "Gebelik",
  "Menopoz",
  "Cinsel Sağlık",
  "<PERSON>slen<PERSON>",
];

const Academy = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("free");
  const [selectedCategory, setSelectedCategory] = useState("Tümü");
  const [searchQuery, setSearchQuery] = useState("");

  const handleBack = () => {
    navigate(-1);
  };

  const filteredVideos = mockVideos.filter((video) => {
    const matchesCategory =
      selectedCategory === "Tümü" || video.category === selectedCategory;
    const matchesSearch = video.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pb-2 bg-white">
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-12 h-12"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <div className="flex-1 text-center pr-12">
          <h1 className="text-lg font-bold text-gray-900">Akademi</h1>
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-4 pb-3">
        <div className="relative flex items-center">
          <div className="absolute left-4 z-10">
            <Search className="w-6 h-6 text-gray-500" />
          </div>
          <input
            type="text"
            placeholder="Eğitim ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full h-12 pl-12 pr-4 bg-gray-100 rounded-xl text-gray-900 placeholder-gray-500 border-0 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="pb-3">
        <div className="flex px-4 border-b border-gray-200">
          <button
            onClick={() => setActiveTab("free")}
            className={cn(
              "flex-1 pb-4 px-0 border-b-3 transition-colors",
              activeTab === "free"
                ? "border-gray-300 text-gray-900"
                : "border-transparent text-gray-500",
            )}
          >
            <span className="text-sm font-bold">Ücretsiz Videolar</span>
          </button>
          <button
            onClick={() => setActiveTab("group")}
            className={cn(
              "flex-1 pb-4 px-8 border-b-3 transition-colors",
              activeTab === "group"
                ? "border-gray-300 text-gray-900"
                : "border-transparent text-gray-500",
            )}
          >
            <span className="text-sm font-bold">Grup Eğitimleri</span>
          </button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="px-3 pb-4">
        <div className="flex gap-3 overflow-x-auto scrollbar-hide">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={cn(
                "flex-shrink-0 px-4 h-8 rounded-xl text-sm font-medium transition-colors",
                selectedCategory === category
                  ? "bg-gray-100 text-gray-900"
                  : "bg-gray-100 text-gray-900",
              )}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Video Content */}
      <div className="px-4 space-y-4 pb-24">
        {filteredVideos.map((video) => (
          <div key={video.id} className="bg-white rounded-xl overflow-hidden">
            {/* Video Thumbnail */}
            <div className="relative h-[201px] bg-gradient-to-br from-orange-200 to-orange-300 rounded-xl overflow-hidden">
              {/* Placeholder for video thumbnail with yoga illustration */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-32 h-24 bg-white rounded-lg opacity-90 flex items-center justify-center">
                  <div className="w-16 h-16 bg-orange-400 rounded-full flex items-center justify-center">
                    <div className="w-8 h-8 bg-orange-300 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Video Info */}
            <div className="p-4">
              <h3 className="text-lg font-bold text-gray-900 mb-1">
                {video.title}
              </h3>
              <div className="flex justify-between items-end">
                <span className="text-gray-500 text-base">
                  {video.duration}
                </span>
              </div>
            </div>

            {/* Engagement Stats */}
            <div className="flex justify-between items-center px-4 pb-4">
              <div className="flex items-center gap-2">
                <Heart className="w-6 h-6 text-gray-500" />
                <span className="text-sm font-bold text-gray-500">
                  {video.likes}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <MessageCircle className="w-6 h-6 text-gray-500" />
                <span className="text-sm font-bold text-gray-500">
                  {video.comments}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Share className="w-6 h-6 text-gray-500" />
                <span className="text-sm font-bold text-gray-500">
                  {video.shares}
                </span>
              </div>
            </div>
          </div>
        ))}

        {filteredVideos.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              No videos found matching your criteria.
            </p>
          </div>
        )}
      </div>

      {/* Bottom Navigation Space */}
      <div className="h-20"></div>
    </div>
  );
};

export default Academy;
