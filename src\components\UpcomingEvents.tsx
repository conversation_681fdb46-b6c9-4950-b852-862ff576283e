const events = [
  {
    id: 1,
    title: "Webinar: Understanding PCOS",
    doctor: "Dr. <PERSON>",
    image: "/placeholder.svg",
    color: "bg-green-500",
  },
  {
    id: 2,
    title: "Webinar: Fertility Options",
    doctor: "Dr. <PERSON>",
    image: "/placeholder.svg",
    color: "bg-orange-200",
  },
];

const UpcomingEvents = () => {
  return (
    <div className="mb-8">
      <h2 className="text-xl font-bold text-gray-900 mb-4">Upcoming Events</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {events.map((event) => (
          <div
            key={event.id}
            className="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div className={`h-32 ${event.color} relative`}>
              <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent"></div>
              <div className="absolute top-4 left-4 text-white">
                <span className="text-sm font-medium">Webinar</span>
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-semibold text-gray-900 mb-1">
                {event.title.replace("Webinar: ", "")}
              </h3>
              <p className="text-sm text-purple-600 font-medium">
                {event.doctor}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UpcomingEvents;
