import { useState } from "react";
import {
  Edit,
  Search,
  Calendar,
  TrendingUp,
  Lightbulb,
  HelpCircle,
  Bot,
  ClipboardList,
} from "lucide-react";
import { cn } from "@/lib/utils";
import BottomNavigation from "@/components/BottomNavigation";

interface Message {
  id: number;
  type:
    | "appointment"
    | "doctor"
    | "health"
    | "recommendation"
    | "support"
    | "assistant"
    | "survey";
  icon: any;
  title: string;
  description: string;
  timestamp: string;
  avatar?: string;
  category: "all" | "doctors" | "reminders" | "support";
}

const mockMessages: Message[] = [
  {
    id: 1,
    type: "appointment",
    icon: Calendar,
    title: "Appointment Reminder",
    description: "Your consultation with Dr<PERSON> tomorrow at 14:00",
    timestamp: "1d",
    category: "reminders",
  },
  {
    id: 2,
    type: "doctor",
    icon: null,
    title: "Dr. <PERSON><PERSON><PERSON>ZDEMİR",
    description: "Hi, how are you feeling today?",
    timestamp: "2d",
    avatar: "/api/placeholder/56/56",
    category: "doctors",
  },
  {
    id: 3,
    type: "health",
    icon: TrendingUp,
    title: "Health Record",
    description: "Cycle data updated - Day 14 (Ovulation)",
    timestamp: "3d",
    category: "reminders",
  },
  {
    id: 4,
    type: "recommendation",
    icon: Lightbulb,
    title: "Recommended: Prenatal",
    description: "Explore our new course on prenatal nutrition.",
    timestamp: "4d",
    category: "support",
  },
  {
    id: 5,
    type: "support",
    icon: HelpCircle,
    title: "Managing morning sickness",
    description: "3 answers",
    timestamp: "5d",
    category: "support",
  },
  {
    id: 6,
    type: "assistant",
    icon: Bot,
    title: "AZMed+ Assistant",
    description: "I'm here to assist you with any questions.",
    timestamp: "6d",
    category: "support",
  },
  {
    id: 7,
    type: "survey",
    icon: ClipboardList,
    title: "Rate your consultation - 2 min",
    description: "Share your feedback on your recent consultation.",
    timestamp: "7d",
    category: "support",
  },
];

const tabs = [
  { id: "all", label: "All" },
  { id: "doctors", label: "Doctors" },
  { id: "reminders", label: "Reminders" },
  { id: "support", label: "Support" },
];

const Messages = () => {
  const [activeTab, setActiveTab] = useState<
    "all" | "doctors" | "reminders" | "support"
  >("all");
  const [searchQuery, setSearchQuery] = useState("");

  const filteredMessages = mockMessages.filter((message) => {
    const matchesTab = activeTab === "all" || message.category === activeTab;
    const matchesSearch =
      message.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      message.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesTab && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gray-50 px-4 pt-4 pb-2">
        <div className="flex justify-between items-center h-12">
          <div className="flex-1" />
          <button className="w-12 h-12 flex items-center justify-center">
            <Edit className="w-6 h-6 text-gray-900" />
          </button>
        </div>
        <div className="mt-2">
          <h1 className="text-3xl font-bold text-gray-900 font-lexend">
            Messages
          </h1>
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-4 pt-3 pb-3 bg-gray-50">
        <div className="relative flex items-center">
          <div className="absolute left-4 z-10">
            <Search className="w-6 h-6 text-purple-400" />
          </div>
          <input
            type="text"
            placeholder="Search messages"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full h-12 pl-12 pr-4 bg-purple-50 rounded-xl text-gray-900 placeholder-purple-400 border-0 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="pb-3 bg-gray-50">
        <div className="flex px-4 border-b border-purple-100">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                "flex-1 pb-4 px-0 border-b-3 transition-colors text-center",
                activeTab === tab.id
                  ? "border-gray-300 text-gray-900"
                  : "border-transparent text-purple-400",
              )}
            >
              <span className="text-sm font-bold font-lexend">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Messages List */}
      <div className="bg-gray-50 pb-24">
        {filteredMessages.map((message) => {
          const IconComponent = message.icon;

          return (
            <div
              key={message.id}
              className="min-h-[72px] px-4 py-2 flex items-center justify-between bg-gray-50"
            >
              <div className="flex items-center gap-4">
                {/* Icon or Avatar */}
                {message.avatar ? (
                  <img
                    src={message.avatar}
                    alt={message.title}
                    className="w-14 h-14 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 flex items-center justify-center rounded-lg bg-purple-50">
                    {IconComponent && (
                      <IconComponent className="w-6 h-6 text-gray-900" />
                    )}
                  </div>
                )}

                {/* Message Content */}
                <div className="flex flex-col justify-center">
                  <div className="w-[262px]">
                    <h3 className="text-base font-medium text-gray-900 font-lexend leading-6">
                      {message.title}
                    </h3>
                  </div>
                  <div className="w-[262px]">
                    <p className="text-sm text-purple-400 font-lexend leading-5">
                      {message.description}
                    </p>
                  </div>
                </div>
              </div>

              {/* Timestamp */}
              <div className="flex flex-col items-end">
                <span className="text-sm text-purple-400 font-lexend leading-5">
                  {message.timestamp}
                </span>
              </div>
            </div>
          );
        })}

        {filteredMessages.length === 0 && (
          <div className="text-center py-12">
            <p className="text-purple-400">
              No messages found matching your criteria.
            </p>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
};

export default Messages;
