import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Calendar } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import BottomNavigation from "../components/BottomNavigation";

const doctors = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    specialty: "Obstetrics & Gynecology, City Hospital",
    rating: "4.8",
    consultations: "120",
    avatar: "bg-teal-400",
  },
  {
    id: 2,
    name: "Dr. <PERSON>",
    specialty: "Cardiology, University Medical Center",
    rating: "4.9",
    consultations: "150",
    avatar: "bg-teal-500",
  },
  {
    id: 3,
    name: "Dr. <PERSON>",
    specialty: "Pediatrics, Children's Hospital",
    rating: "4.7",
    consultations: "100",
    avatar: "bg-gray-300",
  },
  {
    id: 4,
    name: "Dr. <PERSON>",
    specialty: "Dermatology, Skin Care Clinic",
    rating: "4.6",
    consultations: "80",
    avatar: "bg-teal-400",
  },
];

const Consultation = () => {
  const navigate = useNavigate();
  const [symptoms, setSymptoms] = useState("");
  const [activeFilter, setActiveFilter] = useState("Available Now");

  const filters = ["Available Now", "Highest Rated", "Lowest Price"];

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col pb-20">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-4 bg-white">
        <button
          onClick={handleGoBack}
          className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <h1 className="flex-1 text-center text-lg font-bold text-gray-900">
          Online Consultation
        </h1>
        <button className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-lg transition-colors">
          <Settings className="w-6 h-6 text-gray-900" />
        </button>
      </div>

      {/* Symptoms Input */}
      <div className="px-4 mb-4">
        <div className="bg-gray-100 rounded-xl p-4 min-h-[144px]">
          <textarea
            value={symptoms}
            onChange={(e) => setSymptoms(e.target.value)}
            placeholder="Describe your symptoms or concerns..."
            className="w-full h-24 bg-transparent text-lg font-bold text-gray-900 placeholder-gray-600 resize-none outline-none"
            maxLength={500}
          />
        </div>
      </div>

      {/* Character Counter */}
      <div className="px-4 mb-6">
        <div className="flex items-center space-x-2">
          <Mic className="w-6 h-6 text-purple-400" />
          <span className="text-sm font-bold text-purple-400">
            {symptoms.length}/500
          </span>
        </div>
      </div>

      {/* Analysis Section */}
      <div className="px-4 mb-4">
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          Analyzing your symptoms...
        </h3>
        <div className="w-full bg-gray-200 rounded h-2 mb-6">
          <div
            className="bg-gray-900 h-2 rounded"
            style={{ width: "45%" }}
          ></div>
        </div>
      </div>

      {/* Analysis Result */}
      <div className="px-4 mb-6">
        <div className="flex items-center space-x-4 bg-white p-4 rounded-lg">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
            <Heart className="w-6 h-6 text-gray-900" />
          </div>
          <div className="flex-1">
            <p className="font-medium text-gray-900">
              Women's Health & Obstetrics
            </p>
            <p className="text-sm text-purple-600">Confidence: 95%</p>
          </div>
        </div>
      </div>

      {/* Available Doctors */}
      <div className="px-4 mb-4">
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          Available Doctors
        </h3>

        {/* Filter Tabs */}
        <div className="flex space-x-3 mb-4">
          {filters.map((filter) => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter)}
              className={`px-4 py-2 rounded-xl text-sm font-medium transition-colors ${
                activeFilter === filter
                  ? "bg-gray-900 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {filter}
            </button>
          ))}
        </div>

        {/* Doctors Grid */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          {doctors.map((doctor) => (
            <div
              key={doctor.id}
              className="bg-white rounded-lg p-3 text-center"
            >
              <div
                className={`w-16 h-16 ${doctor.avatar} rounded-full mx-auto mb-3 flex items-center justify-center`}
              >
                <div className="w-8 h-8 bg-white/20 rounded-full"></div>
              </div>
              <h4 className="font-medium text-gray-900 text-sm mb-1">
                {doctor.name}
              </h4>
              <p className="text-xs text-purple-600 mb-2">{doctor.specialty}</p>
              <p className="text-xs text-purple-600">
                ★ {doctor.rating} ({doctor.consultations} consultations)
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Emergency Care Card */}
      <div className="px-4 mb-6">
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative">
            <h3 className="text-xl font-bold mb-1">Need Urgent Care?</h3>
            <p className="text-green-100">24/7 Emergency Line</p>
          </div>
        </div>
      </div>

      {/* Book Consultation Button */}
      <div className="px-5 pb-5">
        <button className="w-full bg-purple-600 text-white py-4 rounded-xl font-bold text-lg flex items-center justify-center space-x-3 hover:bg-purple-700 transition-colors">
          <Calendar className="w-6 h-6" />
          <span>Book Consultation</span>
        </button>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
};

export default Consultation;
