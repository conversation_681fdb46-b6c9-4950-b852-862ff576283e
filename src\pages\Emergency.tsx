import { ArrowLef<PERSON>, Phone, Video } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Emergency = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-4 bg-white">
        <button
          onClick={handleGoBack}
          className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <h1 className="flex-1 text-center text-lg font-bold text-gray-900 pr-12">
          A<PERSON>l <PERSON><PERSON>
        </h1>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Title Section */}
        <div className="px-4 py-5 text-center">
          <h2 className="text-xl font-bold text-gray-900">
            7/24 Uzman Desteği
          </h2>
        </div>

        {/* Call Options */}
        <div className="px-4 space-y-4 flex-1">
          {/* Voice Call Option */}
          <div className="bg-white rounded-lg overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-pink-200 to-pink-300 flex items-center justify-center">
              <div className="w-20 h-32 bg-gray-800 rounded-xl flex items-center justify-center relative">
                <div className="w-4 h-1 bg-gray-700 rounded-full absolute top-4"></div>
                <Phone className="w-8 h-8 text-white" />
                <div className="w-4 h-4 bg-gray-700 rounded-full absolute bottom-4"></div>
              </div>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-bold text-gray-900 mb-1">
                Sesli Görüşme
              </h3>
              <p className="text-purple-600">Sesli Görüşme</p>
            </div>
          </div>

          {/* Video Call Option */}
          <div className="bg-white rounded-lg overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-pink-200 to-pink-300 flex items-center justify-center">
              <div className="flex items-center justify-center">
                <div className="w-16 h-12 bg-teal-500 rounded-lg flex items-center justify-center relative">
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    <div className="flex space-x-1">
                      <div className="w-3 h-3 bg-teal-600 rounded-full"></div>
                      <div className="w-3 h-3 bg-teal-600 rounded-full"></div>
                    </div>
                  </div>
                  <Video className="w-6 h-6 text-white" />
                  <div className="w-2 h-2 bg-teal-700 rounded-full absolute bottom-1 right-2"></div>
                </div>
              </div>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-bold text-gray-900 mb-1">
                Görüntülü Görüşme
              </h3>
              <p className="text-purple-600">Görüntülü Görüşme</p>
            </div>
          </div>

          {/* Pricing Info Card */}
          <div className="h-24 bg-gradient-to-r from-gray-400 to-gray-600 rounded-lg flex items-end text-white p-4 relative overflow-hidden">
            <div className="absolute inset-0 bg-black/40"></div>
            <div className="relative">
              <p className="text-lg font-bold">
                Başlangıç ücreti: 200 TL (15 dakika dahil)
              </p>
            </div>
          </div>
        </div>

        {/* Doctor Info */}
        <div className="px-4 py-4 flex items-center space-x-4 bg-white">
          <div className="w-14 h-14 bg-teal-500 rounded-full flex items-center justify-center">
            <div className="w-8 h-8 bg-teal-600 rounded-full"></div>
          </div>
          <div className="flex-1">
            <p className="font-medium text-gray-900">
              Nöbetçi Doktor: Dr. Ayşe Kaya
            </p>
            <p className="text-sm text-purple-600">Kadın Doğum Uzmanı</p>
          </div>
        </div>

        {/* Emergency Warning */}
        <div className="px-4 py-3 text-center">
          <p className="text-sm text-purple-600">
            Acil durumlarda 112'yi arayın
          </p>
        </div>
      </div>

      {/* Bottom Button */}
      <div className="p-4 bg-white">
        <button className="w-full bg-purple-600 text-white py-3 rounded-lg font-bold text-lg hover:bg-purple-700 transition-colors">
          Acil Bağlan
        </button>
        <div className="h-5"></div>
      </div>
    </div>
  );
};

export default Emergency;
