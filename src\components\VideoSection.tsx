const videos = [
  {
    id: 1,
    title: "Understanding Endometriosis",
    doctor: "Dr. <PERSON>",
    thumbnail: "/placeholder.svg",
    gradient: "from-blue-400 to-blue-600",
  },
  {
    id: 2,
    title: "Managing Menopause",
    doctor: "Dr. <PERSON>",
    thumbnail: "/placeholder.svg",
    gradient: "from-teal-400 to-cyan-600",
  },
];

const VideoSection = () => {
  return (
    <div className="mb-8">
      <h2 className="text-xl font-bold text-gray-900 mb-4">
        Most Watched Videos
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {videos.map((video) => (
          <div
            key={video.id}
            className="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div
              className={`h-32 bg-gradient-to-br ${video.gradient} relative`}
            >
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <div className="w-0 h-0 border-l-[8px] border-l-white border-y-[6px] border-y-transparent ml-1"></div>
                </div>
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-semibold text-gray-900 mb-1">
                {video.title}
              </h3>
              <p className="text-sm text-purple-600 font-medium">
                {video.doctor}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VideoSection;
