import {
  Video,
  Calendar,
  Heart,
  MessageCircle,
  Clock,
  GraduationCap,
  Activity,
  Shield,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const services = [
  {
    id: 1,
    title: "Online",
    subtitle: "Consultation",
    icon: Video,
    iconColor: "text-blue-600",
    path: "/consultation",
  },
  {
    id: 2,
    title: "Cycle Tracking",
    subtitle: "",
    icon: Calendar,
    iconColor: "text-pink-600",
    path: "/cycle-tracking",
  },
  {
    id: 3,
    title: "Sexual",
    subtitle: "Therapy",
    icon: Heart,
    iconColor: "text-red-600",
    path: "/sexual-therapy",
  },
  {
    id: 4,
    title: "Egg Reserve",
    subtitle: "",
    icon: MessageCircle,
    iconColor: "text-green-600",
    path: "/egg-reserve",
  },
  {
    id: 5,
    title: "Menstrual",
    subtitle: "Tracking",
    icon: Clock,
    iconColor: "text-purple-600",
    path: "/menstrual-tracking",
  },
  {
    id: 6,
    title: "Academy",
    subtitle: "",
    icon: GraduationCap,
    iconColor: "text-indigo-600",
    path: "/academy",
  },
  {
    id: 7,
    title: "Healthy Living",
    subtitle: "",
    icon: Activity,
    iconColor: "text-orange-600",
    path: null,
  },
  {
    id: 8,
    title: "Cancer",
    subtitle: "Screening",
    icon: Shield,
    iconColor: "text-teal-600",
    path: null,
  },
];

const ServiceGrid = () => {
  const navigate = useNavigate();

  const handleServiceClick = (service: (typeof services)[0]) => {
    if (service.path) {
      navigate(service.path);
    }
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      {services.map((service) => {
        const IconComponent = service.icon;
        return (
          <div
            key={service.id}
            onClick={() => handleServiceClick(service)}
            className={`bg-white rounded-xl p-4 border border-gray-100 hover:shadow-md transition-shadow ${
              service.path ? "cursor-pointer" : "cursor-default"
            }`}
          >
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <IconComponent className={`w-6 h-6 ${service.iconColor}`} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-gray-900 truncate">
                  {service.title}
                </p>
                {service.subtitle && (
                  <p className="text-sm font-semibold text-gray-900">
                    {service.subtitle}
                  </p>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ServiceGrid;
