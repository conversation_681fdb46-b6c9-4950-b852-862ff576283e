import { ArrowLeft, HelpCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

const EggReserve = () => {
  const navigate = useNavigate();
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>("Evet");

  const handleGoBack = () => {
    navigate(-1);
  };

  const progressDots = Array.from({ length: 7 }, (_, index) => index);

  return (
    <div className="min-h-screen bg-purple-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-4 bg-purple-50">
        <button
          onClick={handleGoBack}
          className="w-12 h-12 flex items-center justify-center hover:bg-purple-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <div className="flex-1"></div>
        <button className="w-12 h-12 flex items-center justify-center hover:bg-purple-100 rounded-lg transition-colors">
          <HelpCircle className="w-6 h-6 text-gray-900" />
        </button>
      </div>

      {/* Progress Dots */}
      <div className="flex justify-center items-center py-5 space-x-3">
        {progressDots.map((dot, index) => (
          <div
            key={dot}
            className={`w-2 h-2 rounded-full ${
              index === 0 ? "bg-purple-500" : "bg-purple-200"
            }`}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col px-4">
        {/* Question */}
        <div className="py-5 mb-8">
          <h1 className="text-2xl font-bold text-gray-900 leading-tight">
            Yaşınız 35'in üzerinde mi?
          </h1>
        </div>

        {/* Answer Buttons */}
        <div className="space-y-3 max-w-md mx-auto w-full">
          <button
            onClick={() => setSelectedAnswer("Evet")}
            className={`w-full py-3 px-5 rounded-xl font-bold text-base transition-colors ${
              selectedAnswer === "Evet"
                ? "bg-purple-500 text-white"
                : "bg-purple-100 text-gray-900 hover:bg-purple-200"
            }`}
          >
            Evet
          </button>

          <button
            onClick={() => setSelectedAnswer("Hayır")}
            className={`w-full py-3 px-5 rounded-xl font-bold text-base transition-colors ${
              selectedAnswer === "Hayır"
                ? "bg-purple-500 text-white"
                : "bg-purple-100 text-gray-900 hover:bg-purple-200"
            }`}
          >
            Hayır
          </button>
        </div>

        {/* Spacer */}
        <div className="flex-1"></div>
      </div>
    </div>
  );
};

export default EggReserve;
