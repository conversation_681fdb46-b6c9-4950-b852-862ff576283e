import { useNavigate } from "react-router-dom";

interface EmergencyCardProps {
  className?: string;
}

const EmergencyCard = ({ className = "" }: EmergencyCardProps) => {
  const navigate = useNavigate();

  const handleCallClick = () => {
    navigate("/emergency");
  };

  return (
    <div
      className={`bg-gradient-to-br from-green-400 to-green-700 rounded-2xl p-6 text-white ${className}`}
    >
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold mb-1">Emergency</h2>
          <h3 className="text-2xl font-bold mb-2">Support Line</h3>
          <p className="text-green-100 text-sm">7/24 Expert Support</p>
        </div>
        <button
          onClick={handleCallClick}
          className="bg-purple-600 hover:bg-purple-700 px-6 py-3 rounded-xl font-semibold transition-colors"
        >
          Call
        </button>
      </div>
    </div>
  );
};

export default EmergencyCard;
