import { ArrowLeft, Stethoscope, Calendar, Heart, Edit3 } from "lucide-react";
import { useNavigate } from "react-router-dom";

const MenstrualTracking = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  const quickActions = [
    {
      id: 1,
      title: "Sempt<PERSON>",
      subtitle: "<PERSON><PERSON>",
      icon: Stethoscope,
    },
    {
      id: 2,
      title: "Regl",
      subtitle: "Başlangıcı",
      icon: Calendar,
    },
    {
      id: 3,
      title: "Ruh Hali",
      subtitle: "",
      icon: Heart,
    },
    {
      id: 4,
      title: "Notlar",
      subtitle: "",
      icon: Edit3,
    },
  ];

  return (
    <div className="min-h-screen bg-white flex flex-col pb-20">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-4 bg-white">
        <button
          onClick={handleGoBack}
          className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <h1 className="flex-1 text-center text-lg font-bold text-gray-900 pr-12">
          Regl Takibi
        </h1>
      </div>

      {/* Cycle Progress */}
      <div className="px-4 mb-8">
        <div className="flex justify-between items-center mb-3">
          <div></div>
          <span className="text-sm text-gray-900">14</span>
        </div>
        <div className="w-full bg-gray-200 rounded h-2">
          <div
            className="bg-gray-900 h-2 rounded"
            style={{ width: "40%" }}
          ></div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-4 mb-8">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Hızlı Eylemler</h2>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => {
            const IconComponent = action.icon;
            return (
              <button
                key={action.id}
                className="flex items-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <IconComponent className="w-6 h-6 text-gray-900" />
                <div className="text-left">
                  <p className="font-bold text-gray-900">{action.title}</p>
                  {action.subtitle && (
                    <p className="font-bold text-gray-900">{action.subtitle}</p>
                  )}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Statistics */}
      <div className="px-4 mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          Bu Ay İstatistikleri
        </h2>
        <div className="flex justify-between items-start border-t border-gray-200 pt-5">
          <div className="flex-1">
            <p className="text-sm text-gray-600 mb-1">Ortalama</p>
            <p className="text-sm text-gray-600 mb-1">Döngü</p>
            <p className="text-sm text-gray-600 mb-2">Süreniz</p>
            <p className="text-sm text-gray-900 font-medium">28 gün</p>
          </div>
          <div className="flex-1 ml-6">
            <p className="text-sm text-gray-600 mb-1">
              Bir Sonraki Döngü Tahmini
            </p>
            <p className="text-sm text-gray-900 font-medium mt-4">29 Mayıs</p>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex justify-around items-center">
          <div className="flex flex-col items-center space-y-1 py-2 px-3 min-w-0 flex-1 text-gray-500">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
            </svg>
            <span className="text-xs font-medium">Ana Sayfa</span>
          </div>
          <div className="flex flex-col items-center space-y-1 py-2 px-3 min-w-0 flex-1 text-gray-500">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
            </svg>
            <span className="text-xs font-medium">Randevular</span>
          </div>
          <div className="flex flex-col items-center space-y-1 py-2 px-3 min-w-0 flex-1 text-gray-500">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
            </svg>
            <span className="text-xs font-medium">Mesajlar</span>
          </div>
          <div className="flex flex-col items-center space-y-1 py-2 px-3 min-w-0 flex-1 text-gray-500">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 6C14.3 6 13.7 6.3 13.2 6.8L9 11H7V13H9.2L12.8 9.4L14.8 16H17L14.4 8L18 9H21Z" />
            </svg>
            <span className="text-xs font-medium">Profil</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MenstrualTracking;
