import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ting<PERSON>, Shield } from "lucide-react";
import { useNavigate } from "react-router-dom";

const therapists = [
  {
    id: 1,
    name: "Dr. <PERSON><PERSON><PERSON><PERSON>",
    clinic: "Özel Terapi Merkezi",
    specialty: "Cinsel Terapi Uzmanı",
    avatar: "bg-orange-200",
  },
  {
    id: 2,
    name: "Dr. <PERSON>",
    clinic: "Özel Terapi Merkezi",
    specialty: "Cinsel Terapi Uzmanı",
    avatar: "bg-orange-300",
  },
  {
    id: 3,
    name: "Dr. <PERSON><PERSON>",
    clinic: "Özel Terapi Merkezi",
    specialty: "Cinsel Terapi Uzmanı",
    avatar: "bg-orange-200",
  },
];

const SexualTherapy = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-4 bg-white">
        <button
          onClick={handleGoBack}
          className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <h1 className="flex-1 text-center text-lg font-bold text-gray-900">
          Cinsel Terapi
        </h1>
        <button className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-lg transition-colors">
          <Settings className="w-6 h-6 text-gray-900" />
        </button>
      </div>

      {/* Privacy Section */}
      <div className="px-4 mb-6">
        <div className="flex items-start space-x-4 bg-gray-50 rounded-xl p-4">
          <div className="flex-1">
            <p className="text-sm text-gray-600 mb-1">Gizlilik</p>
            <p className="text-base font-bold text-gray-900 leading-tight">
              Görüşmeleriniz tamamen gizlidir ve kayıt altına alınmaz
            </p>
          </div>
          <div className="w-20 h-20 bg-gray-200 rounded-xl flex items-center justify-center">
            <Shield className="w-10 h-10 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Expert Therapists Section */}
      <div className="px-4 mb-4">
        <h2 className="text-xl font-bold text-gray-900 mb-1">
          Uzman Terapistler
        </h2>
        <p className="text-base text-gray-900 mb-6">
          Size en uygun uzmanı seçin
        </p>
      </div>

      {/* Therapists List */}
      <div className="flex-1">
        {therapists.map((therapist) => (
          <div key={therapist.id} className="px-4 py-3 bg-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div
                  className={`w-16 h-16 ${therapist.avatar} rounded-full flex items-center justify-center`}
                >
                  <div className="w-8 h-8 bg-white/30 rounded-full"></div>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 mb-1">
                    {therapist.name}
                  </h3>
                  <p className="text-sm text-gray-600">{therapist.clinic}</p>
                  <p className="text-sm text-gray-600">{therapist.specialty}</p>
                </div>
              </div>
              <button className="px-4 py-2 bg-gray-100 text-gray-900 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                Randevu Al
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Payment Section */}
      <div className="px-4 py-4 bg-white border-t border-gray-100">
        <div className="flex items-center justify-between bg-gray-50 rounded-xl p-4">
          <div className="flex-1">
            <p className="text-sm text-gray-600 mb-1">Ödeme Bilgileri</p>
            <p className="text-base font-bold text-gray-900 mb-4">
              Ödeme Tutarı: 500 TL
            </p>
            <button className="px-4 py-2 bg-gray-200 text-gray-900 rounded-xl font-medium hover:bg-gray-300 transition-colors">
              Ödemeyi Tamamla
            </button>
          </div>
          <div className="w-20 h-20 bg-teal-500 rounded-xl flex items-center justify-center ml-4">
            <div className="w-12 h-8 bg-teal-600 rounded-md flex items-center justify-center">
              <div className="w-8 h-6 bg-white/20 rounded-sm"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SexualTherapy;
