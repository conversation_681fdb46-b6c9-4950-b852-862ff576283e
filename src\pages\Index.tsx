import { Search, Settings } from "lucide-react";
import EmergencyCard from "../components/EmergencyCard";
import ServiceGrid from "../components/ServiceGrid";
import UpcomingEvents from "../components/UpcomingEvents";
import VideoSection from "../components/VideoSection";
import BottomNavigation from "../components/BottomNavigation";

const Index = () => {
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-white px-4 py-4 flex items-center justify-between border-b border-gray-100">
        <h1 className="text-xl font-bold text-gray-900">AZMed+</h1>
        <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
          <Settings className="w-6 h-6 text-gray-600" />
        </button>
      </div>

      {/* Search Bar */}
      <div className="px-4 py-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="How can we help you?"
            className="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-xl text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-white transition-all"
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 space-y-6">
        {/* Emergency Support */}
        <EmergencyCard />

        {/* Services Grid */}
        <div>
          <ServiceGrid />
        </div>

        {/* Upcoming Events */}
        <UpcomingEvents />

        {/* Most Watched Videos */}
        <VideoSection />
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
};

export default Index;
