import { useState } from "react";
import { ArrowLeft, Search, ChevronDown } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import BottomNavigation from "@/components/BottomNavigation";

interface Doctor {
  id: number;
  name: string;
  specialization: string;
  rating: number;
  reviews: number;
  membershipIncluded: boolean;
  image: string;
}

const mockDoctors: Doctor[] = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    specialization: "Gynecologist",
    rating: 4.9,
    reviews: 120,
    membershipIncluded: true,
    image: "/api/placeholder/130/118",
  },
  {
    id: 2,
    name: "<PERSON>. <PERSON>",
    specialization: "Obstetrician",
    rating: 4.8,
    reviews: 150,
    membershipIncluded: true,
    image: "/api/placeholder/130/118",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    specialization: "Fertility Specialist",
    rating: 4.7,
    reviews: 100,
    membershipIncluded: true,
    image: "/api/placeholder/130/139",
  },
  {
    id: 4,
    name: "<PERSON><PERSON> <PERSON>",
    specialization: "Gynecologist",
    rating: 4.6,
    reviews: 80,
    membershipIncluded: false,
    image: "/api/placeholder/130/118",
  },
];

const filterOptions = [
  { id: "specialization", label: "Specialization", hasDropdown: true },
  { id: "availability", label: "Availability", hasDropdown: true },
  { id: "language", label: "Language", hasDropdown: false },
  { id: "rating", label: "Rating", hasDropdown: false },
];

const Appointments = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);

  const handleBack = () => {
    navigate(-1);
  };

  const handleBookAppointment = (doctorId: number) => {
    // Handle booking logic here
    console.log(`Booking appointment with doctor ${doctorId}`);
  };

  const filteredDoctors = mockDoctors.filter(
    (doctor) =>
      doctor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doctor.specialization.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pb-2 bg-white">
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-12 h-12"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <div className="flex-1 text-center pr-12">
          <h1 className="text-lg font-bold text-gray-900 font-lexend">
            Find a doctor
          </h1>
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-4 pb-3">
        <div className="relative flex items-center">
          <div className="absolute left-4 z-10">
            <Search className="w-6 h-6 text-gray-500" />
          </div>
          <input
            type="text"
            placeholder="Search for a doctor"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full h-12 pl-12 pr-4 bg-gray-100 rounded-xl text-gray-900 placeholder-gray-500 border-0 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Filter Buttons */}
      <div className="px-3 pb-4">
        <div className="flex gap-3 overflow-x-auto scrollbar-hide">
          {filterOptions.map((filter) => (
            <button
              key={filter.id}
              className="flex-shrink-0 flex items-center gap-2 px-4 h-8 rounded-xl bg-gray-100 text-gray-900 text-sm font-medium transition-colors hover:bg-gray-200"
            >
              <span>{filter.label}</span>
              {filter.hasDropdown && <ChevronDown className="w-4 h-4" />}
            </button>
          ))}
        </div>
      </div>

      {/* Doctor List */}
      <div className="px-4 space-y-4 pb-24">
        {filteredDoctors.map((doctor) => (
          <div key={doctor.id} className="bg-white rounded-xl overflow-hidden">
            <div className="flex justify-between items-start">
              {/* Doctor Info */}
              <div className="flex-1 max-w-[228px] flex flex-col gap-4">
                <div className="flex flex-col gap-1">
                  <div className="text-sm text-gray-500 font-lexend">
                    {doctor.membershipIncluded
                      ? "Membership included"
                      : "Additional cost"}
                  </div>
                  <h3 className="text-base font-bold text-gray-900 font-lexend leading-5">
                    {doctor.name}
                  </h3>
                  <p className="text-sm text-gray-500 font-lexend leading-5">
                    {doctor.specialization} · {doctor.rating} ({doctor.reviews}{" "}
                    reviews)
                  </p>
                </div>

                {/* Book Button */}
                <button
                  onClick={() => handleBookAppointment(doctor.id)}
                  className="w-[84px] h-8 px-4 bg-gray-100 rounded-xl text-sm font-medium text-gray-900 font-lexend hover:bg-gray-200 transition-colors"
                >
                  Book
                </button>
              </div>

              {/* Doctor Image */}
              <div className="flex-shrink-0 ml-4">
                <div className="w-[130px] h-[118px] bg-gradient-to-br from-teal-300 to-teal-400 rounded-xl flex items-center justify-center overflow-hidden">
                  {/* Placeholder for doctor illustration */}
                  <div className="w-24 h-20 bg-white/20 rounded-lg flex items-center justify-center">
                    <div className="w-16 h-16 bg-white/30 rounded-full flex items-center justify-center">
                      <div className="w-10 h-10 bg-white/40 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {filteredDoctors.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              No doctors found matching your search criteria.
            </p>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
};

export default Appointments;
