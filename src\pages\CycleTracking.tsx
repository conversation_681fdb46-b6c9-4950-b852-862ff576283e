import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  Stethoscope,
  Calendar,
  Heart,
  Edit3,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import BottomNavigation from "../components/BottomNavigation";

const CycleTracking = () => {
  const navigate = useNavigate();
  const [currentMonth, setCurrentMonth] = useState("May 2024");
  const [selectedDate, setSelectedDate] = useState(5);

  const handleGoBack = () => {
    navigate(-1);
  };

  const daysOfWeek = ["S", "M", "T", "W", "T", "F", "S"];

  // May 2024 calendar data
  const calendarDays = [
    [null, null, null, 1, 2, 3, 4],
    [5, 6, 7, 8, 9, 10, 11],
    [12, 13, 14, 15, 16, 17, 18],
    [19, 20, 21, 22, 23, 24, 25],
    [26, 27, 28, 29, 30, null, null],
  ];

  const quickActions = [
    {
      id: 1,
      title: "<PERSON><PERSON><PERSON>",
      icon: Stethoscope,
    },
    {
      id: 2,
      title: "<PERSON><PERSON> Başlangıcı",
      icon: Calendar,
    },
    {
      id: 3,
      title: "<PERSON><PERSON>i",
      icon: Heart,
    },
    {
      id: 4,
      title: "Notlar",
      icon: Edit3,
    },
  ];

  return (
    <div className="min-h-screen bg-white flex flex-col pb-20">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-4 bg-white">
        <button
          onClick={handleGoBack}
          className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-gray-900" />
        </button>
        <h1 className="flex-1 text-center text-lg font-bold text-gray-900 pr-12">
          Regl Takibi
        </h1>
      </div>

      {/* Cycle Progress */}
      <div className="px-4 mb-6">
        <div className="flex justify-between items-center mb-3">
          <div></div>
          <span className="text-sm text-gray-900">14</span>
        </div>
        <div className="w-full bg-gray-200 rounded h-2">
          <div
            className="bg-gray-900 h-2 rounded"
            style={{ width: "40%" }}
          ></div>
        </div>
      </div>

      {/* Calendar */}
      <div className="px-4 mb-6">
        {/* Calendar Header */}
        <div className="flex items-center justify-between mb-4 px-2">
          <button className="p-2 hover:bg-gray-100 rounded-lg">
            <ChevronLeft className="w-5 h-5 text-gray-900" />
          </button>
          <h2 className="text-base font-bold text-gray-900">{currentMonth}</h2>
          <button className="p-2 hover:bg-gray-100 rounded-lg">
            <ChevronRight className="w-5 h-5 text-gray-900" />
          </button>
        </div>

        {/* Calendar Grid */}
        <div className="bg-white">
          {/* Days of week header */}
          <div className="grid grid-cols-7 gap-0 mb-2">
            {daysOfWeek.map((day, index) => (
              <div
                key={index}
                className="h-12 flex items-center justify-center"
              >
                <span className="text-sm font-bold text-gray-900">{day}</span>
              </div>
            ))}
          </div>

          {/* Calendar days */}
          {calendarDays.map((week, weekIndex) => (
            <div key={weekIndex} className="grid grid-cols-7 gap-0">
              {week.map((day, dayIndex) => (
                <div
                  key={dayIndex}
                  className="h-12 flex items-center justify-center"
                >
                  {day && (
                    <button
                      className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                        day === selectedDate
                          ? "bg-purple-600 text-white"
                          : "text-gray-900 hover:bg-gray-100"
                      }`}
                      onClick={() => setSelectedDate(day)}
                    >
                      {day}
                    </button>
                  )}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-4 mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Hızlı Eylemler</h3>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => {
            const IconComponent = action.icon;
            return (
              <button
                key={action.id}
                className="flex items-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <IconComponent className="w-6 h-6 text-gray-900" />
                <span className="font-bold text-gray-900">{action.title}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Statistics */}
      <div className="px-4 mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">
          Bu Ay İstatistikleri
        </h3>
        <div className="space-y-6">
          <div className="flex justify-between items-start border-t border-gray-200 pt-5">
            <div className="flex-1">
              <p className="text-sm text-gray-600 mb-1">
                Ortalama Döngü Süreniz
              </p>
              <p className="text-sm text-gray-900">28 gün</p>
            </div>
            <div className="flex-1 ml-6">
              <p className="text-sm text-gray-600 mb-1">
                Bir Sonraki Döngü Tahmini
              </p>
              <p className="text-sm text-gray-900">29 Mayıs</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
};

export default CycleTracking;
