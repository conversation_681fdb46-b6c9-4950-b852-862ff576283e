import { Settings, ChevronRight, Plus } from "lucide-react";
import BottomNavigation from "../components/BottomNavigation";

const Profile = () => {
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gray-50 px-4 py-4 flex items-center justify-between">
        <div className="flex-1 flex justify-center">
          <h1 className="text-lg font-bold text-gray-900">My Profile</h1>
        </div>
        <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
          <Settings className="w-6 h-6 text-gray-600" />
        </button>
      </div>

      {/* Profile Section */}
      <div className="px-4 py-4">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-32 h-32 rounded-full bg-gradient-to-br from-orange-200 to-orange-300 flex items-center justify-center overflow-hidden">
            {/* Profile Avatar */}
            <div className="w-full h-full bg-gradient-to-br from-orange-200 to-orange-300 flex items-center justify-center">
              <div className="w-24 h-24 rounded-full bg-orange-300 flex items-center justify-center">
                <span className="text-2xl font-semibold text-orange-800">
                  SC
                </span>
              </div>
            </div>
          </div>
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900">Sophia Carter</h2>
            <p className="text-blue-600 text-sm">Patient ID: 123456789</p>
          </div>
        </div>
      </div>

      {/* Membership Card */}
      <div className="px-4 mb-4">
        <div className="bg-gradient-to-br from-green-400 to-green-600 rounded-xl p-6 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative flex justify-between items-end">
            <div>
              <h3 className="text-xl font-bold mb-1">AZMed+ Membership</h3>
              <p className="text-green-100">Active</p>
            </div>
            <button className="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded-xl text-sm font-bold transition-colors">
              View Details
            </button>
          </div>
        </div>
      </div>

      {/* Health Overview Card */}
      <div className="px-4 mb-6">
        <div className="bg-gradient-to-br from-orange-300 to-orange-400 rounded-xl p-6 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative flex justify-between items-end">
            <div>
              <h3 className="text-xl font-bold mb-1">Health Overview</h3>
              <p className="text-orange-100">Last Updated: 2 days ago</p>
            </div>
            <button className="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded-xl text-sm font-bold transition-colors">
              View Details
            </button>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="px-4 mb-6">
        <div className="flex space-x-3">
          <button className="flex-1 bg-blue-500 text-white py-3 rounded-xl font-bold hover:bg-blue-600 transition-colors">
            Schedule Appointment
          </button>
          <button className="flex-1 bg-gray-200 text-gray-900 py-3 rounded-xl font-bold hover:bg-gray-300 transition-colors">
            Message Doctor
          </button>
        </div>
      </div>

      {/* Account Settings */}
      <div className="mb-6">
        <div className="px-4 py-3">
          <h3 className="text-xl font-bold text-gray-900">Account Settings</h3>
        </div>
        <div className="bg-gray-50">
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50 border-b border-gray-100">
            <span className="text-gray-900">Personal Information</span>
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </div>
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50 border-b border-gray-100">
            <span className="text-gray-900">Notifications</span>
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </div>
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50">
            <span className="text-gray-900">Security</span>
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Family Account */}
      <div className="mb-6">
        <div className="px-4 py-3">
          <h3 className="text-xl font-bold text-gray-900">Family Account</h3>
        </div>
        <div className="bg-gray-50">
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50">
            <span className="text-gray-900">Add Family Member</span>
            <Plus className="w-5 h-5 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="mb-6">
        <div className="px-4 py-3">
          <h3 className="text-xl font-bold text-gray-900">Recent Activities</h3>
        </div>
        <div className="bg-gray-50">
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50 border-b border-gray-100">
            <div>
              <p className="font-medium text-gray-900">May 15, 2024</p>
              <p className="text-sm text-blue-600">
                Appointment with Dr. Harper
              </p>
            </div>
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </div>
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50">
            <div>
              <p className="font-medium text-gray-900">May 10, 2024</p>
              <p className="text-sm text-blue-600">Lab Results</p>
            </div>
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Health Goals */}
      <div className="mb-6">
        <div className="px-4 py-3">
          <h3 className="text-xl font-bold text-gray-900">Health Goals</h3>
        </div>
        <div className="bg-gray-50">
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50">
            <span className="text-gray-900">Manage Goals</span>
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Emergency Contacts */}
      <div className="mb-6">
        <div className="px-4 py-3">
          <h3 className="text-xl font-bold text-gray-900">
            Emergency Contacts
          </h3>
        </div>
        <div className="bg-gray-50">
          <div className="flex items-center justify-between px-4 py-4 bg-gray-50">
            <span className="text-gray-900">View Contacts</span>
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
};

export default Profile;
