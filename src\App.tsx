import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Profile from "./pages/Profile";
import Emergency from "./pages/Emergency";
import Consultation from "./pages/Consultation";
import CycleTracking from "./pages/CycleTracking";
import SexualTherapy from "./pages/SexualTherapy";
import EggReserve from "./pages/EggReserve";
import MenstrualTracking from "./pages/MenstrualTracking";
import Academy from "./pages/Academy";
import Messages from "./pages/Messages";
import Appointments from "./pages/Appointments";
import NotFound from "./pages/NotFound";

const App = () => (
  <BrowserRouter>
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/profile" element={<Profile />} />
      <Route path="/emergency" element={<Emergency />} />
      <Route path="/consultation" element={<Consultation />} />
      <Route path="/cycle-tracking" element={<CycleTracking />} />
      <Route path="/sexual-therapy" element={<SexualTherapy />} />
      <Route path="/egg-reserve" element={<EggReserve />} />
      <Route path="/menstrual-tracking" element={<MenstrualTracking />} />
      <Route path="/academy" element={<Academy />} />
      <Route path="/messages" element={<Messages />} />
      <Route path="/appointments" element={<Appointments />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  </BrowserRouter>
);

export default App;
